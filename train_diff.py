import torch
import torch.nn as nn
from diffusers import DDPMScheduler
import numpy as np
from sklearn.model_selection import train_test_split
import os
from utils import normalizer

# 1. CondMLP定义
class CondMLP(nn.Module):
    def __init__(self, cond_dim=6, out_dim=7, hidden_dim=128):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(out_dim + cond_dim + 1, hidden_dim),  # +1 for timestep embedding
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, out_dim)
        )
    def forward(self, x, cond, t):
        # x: (B, 7), cond: (B, 6), t: (B, 1)
        return self.net(torch.cat([x, cond, t], dim=1))

# 5. 采样函数
@torch.no_grad()
def sample_ddpm(model, cond, scheduler, n_steps=1000, batch_size=10):
    device = next(model.parameters()).device
    x = torch.randn(batch_size, 7, device=device)
    cond = cond.expand(batch_size, -1)
    for t in reversed(range(n_steps)):
        t_tensor = torch.full((batch_size, 1), t / n_steps, device=device)
        noise_pred = model(x, cond, t_tensor)
        x = scheduler.step(noise_pred, t, x).prev_sample
    return x


if __name__ == "__main__":
    # 2. 数据加载
    pose6d = np.load('filtered_pose6d_array.npy').astype('float32')
    q_right = np.load('filtered_q_array_origin.npy')[:, 13:20].astype('float32')

    # X = torch.from_numpy(pose6d)
    # y = torch.from_numpy(q_right)

    norm = normalizer(pose6d, q_right)
    X_normalized = norm.normalize(pose6d, is_train_x=True)
    y_normalized = norm.normalize(q_right, is_train_x=False)
    X = torch.from_numpy(X_normalized.astype('float32'))
    y = torch.from_numpy(y_normalized.astype('float32'))

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 3. Diffusion调度器
    noise_scheduler = DDPMScheduler(num_train_timesteps=1000, beta_schedule="linear")

    # 4. 训练循环
    device = 'cuda'
    model = CondMLP().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    n_epochs = 90000
    batch_size = 128

    for epoch in range(n_epochs):
        # ---- 训练 ----
        model.train()
        idx = torch.randperm(X_train.shape[0])
        X_shuffled = X_train[idx]
        y_shuffled = y_train[idx]
        for i in range(0, X_train.shape[0], batch_size):
            cond = X_shuffled[i:i+batch_size].to(device)
            target = y_shuffled[i:i+batch_size].to(device)
            noise = torch.randn_like(target)
            timesteps = torch.randint(0, noise_scheduler.config.num_train_timesteps, (target.size(0),), device=device).long()
            noisy = noise_scheduler.add_noise(target, noise, timesteps)
            t_emb = timesteps.float().unsqueeze(1) / noise_scheduler.config.num_train_timesteps
            pred = model(noisy, cond, t_emb)
            loss = nn.MSELoss()(pred, noise)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        # ---- 测试集评估 ----
        if (epoch+1) % 100 == 0 or epoch == 0:
            model.eval()
            test_loss = 0
            n_test = X_test.shape[0]
            with torch.no_grad():
                for i in range(0, n_test, batch_size):
                    cond = X_test[i:i+batch_size].to(device)
                    target = y_test[i:i+batch_size].to(device)
                    noise = torch.randn_like(target)
                    timesteps = torch.randint(0, noise_scheduler.config.num_train_timesteps, (target.size(0),), device=device).long()
                    noisy = noise_scheduler.add_noise(target, noise, timesteps)
                    t_emb = timesteps.float().unsqueeze(1) / noise_scheduler.config.num_train_timesteps
                    pred = model(noisy, cond, t_emb)
                    loss = nn.MSELoss()(pred, noise)
                    test_loss += loss.item() * cond.size(0)
            test_loss /= n_test

            print(f"Epoch {epoch+1}, Train Loss: {loss.item():.6f}, Test Loss: {test_loss:.6f}")



    # 6. 采样示例
    model.eval()
    # cond = X[0:1].to(device)  # 1x6
    # samples = sample_ddpm(model, cond, noise_scheduler, n_steps=1000, batch_size=10)
    # # print(samples.cpu().numpy())

    # # 反归一化
    # samples_np = samples.cpu().numpy()  # (10, 7)
    # pred_q = norm.denormalize(samples_np, is_train_x=False)  # (10, 7)
    # true_q = norm.denormalize(y[0:1].cpu().numpy(), is_train_x=False)  # (1, 7)

    # print("真值：", np.round(true_q.flatten(), 3))
    # print("Diffusion采样（反归一化后）：")
    # for i, pred in enumerate(pred_q):
    #     print(f"采样{i+1}：", np.round(pred, 3))

    n_show = 5  # 显示5个测试样本
    n_samples = 3  # 每个样本生成3个不同的预测

    with torch.no_grad():
        for i in range(n_show):
            test_pose = X_test[i:i+1].to(device)  # 取一个测试样本
            
            # 使用diffusion模型生成多个样本
            samples = sample_ddpm(model, test_pose, noise_scheduler, 
                                n_steps=1000, batch_size=n_samples)
            
            # 反归一化
            pred_q = norm.denormalize(samples.cpu().numpy(), is_train_x=False)
            true_q = norm.denormalize(y_test[i:i+1].cpu().numpy(), is_train_x=False)
            
            print(f"\n样本 {i+1}:")
            print("  真实的右臂7关节角度：", np.round(true_q.flatten(), 3))
            print("  Diffusion生成的关节角度：")
            for j, pred in enumerate(pred_q):
                print(f"    生成{j+1}：", np.round(pred, 3))
            
            # 计算预测误差（使用第一个生成样本）
            error = np.abs(pred_q[0] - true_q.flatten())
            print("  预测误差（绝对值）：", np.round(error, 3))
            print("  平均误差：", np.round(np.mean(error), 3))

    os.makedirs('checkpoints/diffusion', exist_ok=True)
    torch.save(model.state_dict(), 'checkpoints/diffusion/model.pt')

    # 保存调度器（可选）
    noise_scheduler.save_pretrained('checkpoints/diffusion/scheduler')