#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Pose
from sensor_msgs.msg import JointState
from std_msgs.msg import Float64MultiArray
import numpy as np
import torch
from interface_utils import predict
from utils import normalizer
import HumanizedJointsPredict

class RobotJointPredictor:
    def __init__(self):
        rospy.init_node('robot_joint_predictor')
        
        # 加载归一化器
        self.norm = normalizer.load("checkpoints/mlp/normalizer.pkl")  # 或其他路径
        
        # 设置设备
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        rospy.loginfo(f"Using device: {self.device}")
        
        # 创建服务
        self.service = rospy.Service('predict_joints', HumanizedJointsPredict, self.predict_callback)
        rospy.loginfo("Robot Joint Predictor Service Started")
        
    def pose_to_pose6d(self, pose_msg):
        """将ROS Pose消息转换为6D pose数组"""
        position = [pose_msg.position.x, pose_msg.position.y, pose_msg.position.z]
        orientation = [pose_msg.orientation.x, pose_msg.orientation.y, pose_msg.orientation.z]
        return np.array(position + orientation, dtype=np.float32)
    
    def predict_callback(self, req):
        """服务回调函数"""
        try:
            # 1. 提取pose6d
            pose6d_raw = self.pose_to_pose6d(req.pose)
            
            # 2. 归一化输入
            pose6d_normalized = self.norm.normalize(pose6d_raw.reshape(1, -1), is_train_x=True).flatten()
            
            # 3. 根据模式进行预测
            mode = req.mode if req.mode else "mlp"  # 默认使用MLP
            n_samples = req.n_samples if req.n_samples > 0 else 1
            
            rospy.loginfo(f"Predicting with mode: {mode}, n_samples: {n_samples}")
            
            # 4. 模型预测（输出是归一化的）
            if mode == "mlp":
                pred_normalized = predict(pose6d_normalized, mode="mlp", device=self.device, n_samples=1)
                joint_angles = self.norm.denormalize(pred_normalized, is_train_x=False).flatten()
            elif mode == "cvae":
                samples_normalized = predict(pose6d_normalized, mode="cvae", device=self.device, n_samples=n_samples)
                samples_denormalized = self.norm.denormalize(samples_normalized, is_train_x=False)
                joint_angles = np.mean(samples_denormalized, axis=0)  # 取平均值
            elif mode == "diffusion":
                samples_normalized = predict(pose6d_normalized, mode="diffusion", device=self.device, n_samples=n_samples)
                samples_denormalized = self.norm.denormalize(samples_normalized, is_train_x=False)
                joint_angles = np.mean(samples_denormalized, axis=0)  # 取平均值
            else:
                raise ValueError(f"Unknown mode: {mode}")
            
            # 5. 构造响应
            response = PredictJointsResponse()
            response.joint_angles = joint_angles.tolist()
            response.success = True
            response.message = f"Successfully predicted using {mode} mode"
            
            rospy.loginfo(f"Predicted joint angles: {np.round(joint_angles, 3)}")
            return response
            
        except Exception as e:
            rospy.logerr(f"Prediction failed: {str(e)}")
            response = PredictJointsResponse()
            response.joint_angles = [0.0] * 7  # 返回零值
            response.success = False
            response.message = f"Prediction failed: {str(e)}"
            return response

if __name__ == '__main__':
    try:
        predictor = RobotJointPredictor()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass