import torch
import numpy as np
import os
import sys
from train_mlp import M<PERSON>
from train_cvae import CVA<PERSON>
from train_diff import CondMLP, sample_ddpm
from diffusers import DDPMScheduler
import argparse
from utils import normalizer


def get_model_and_weight_path(mode):
    if mode == "mlp":
        model_class = MLP
        weight_path = "checkpoints/mlp/model.pt"
        return model_class, weight_path
    elif mode == "cvae":
        model_class = CVAE
        weight_path = "checkpoints/cvae/model.pt"
        return model_class, weight_path
    elif mode == "diffusion":
        model_class = CondMLP
        weight_path = "checkpoints/diffusion/model.pt"
        return model_class, weight_path, sample_ddpm
    else:
        raise ValueError("Unknown mode: choose from 'mlp', 'cvae', 'diffusion'")

def predict(pose6d, mode="mlp", device="cpu", n_samples=1):
    pose6d_tensor = torch.tensor(pose6d, dtype=torch.float32, device=device).unsqueeze(0)  # (1, 6)

    if mode == "mlp":
        model_class, weight_path = get_model_and_weight_path("mlp")
        model = model_class().to(device)
        model.load_state_dict(torch.load(weight_path, map_location=device))
        model.eval()
        with torch.no_grad():
            out = model(pose6d_tensor)
        return out.cpu().numpy()

    elif mode == "cvae":
        model_class, weight_path = get_model_and_weight_path("cvae")
        model = model_class().to(device)
        model.load_state_dict(torch.load(weight_path, map_location=device))
        model.eval()
        z_dim = 8  # 与CVAE定义一致
        with torch.no_grad():
            pose_repeat = pose6d_tensor.expand(n_samples, -1)
            z = torch.randn(n_samples, z_dim, device=device)
            out = model.decode(pose_repeat, z)
        return out.cpu().numpy()

    elif mode == "diffusion":
        model_class, weight_path, sample_ddpm = get_model_and_weight_path("diffusion")
        model = model_class().to(device)
        model.load_state_dict(torch.load(weight_path, map_location=device))
        model.eval()
        scheduler_path = "checkpoints/diffusion/scheduler"
        # 加载 noise scheduler
        scheduler = DDPMScheduler.from_pretrained(scheduler_path) if os.path.exists(scheduler_path) else None
        with torch.no_grad():
            cond = pose6d_tensor
            out = sample_ddpm(model, cond, scheduler, n_steps=1000, batch_size=n_samples)
        return out.cpu().numpy()

    else:
        raise ValueError("Unknown mode")

if __name__ == "__main__":
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="mlp", choices=["mlp", "cvae", "diffusion"])
    parser.add_argument("--device", type=str, default="cpu")
    parser.add_argument("--n_samples", type=int, default=1)
    args = parser.parse_args()

    pose6d_array = np.load('filtered_pose6d_array.npy')  # (N, 6)
    q_array = np.load('filtered_q_array_origin.npy')     # (N, 20)
    right_arm_indices = list(range(13, 20))              # 右臂7关节
    q_right = q_array[:, right_arm_indices]              # (N, 7)
    
    norm = normalizer.load(f"checkpoints/normalizer.pkl")
    idx = np.random.randint(0, pose6d_array.shape[0])
    pose6d = norm.x[idx]
    true_q = norm.y[idx]

    print(f"样本索引: {idx}")
    print("真值:", np.round(true_q.flatten(), 2))

    # MLP
    mlp_pred = predict(pose6d, mode="mlp", device=args.device, n_samples=1)
    print("MLP预测:", np.round(mlp_pred.flatten(), 2))

    # CVAE
    cvae_samples = predict(pose6d, mode="cvae", device=args.device, n_samples=args.n_samples)
    cvae_mean = np.mean(cvae_samples, axis=0)
    print("CVAE预测:", np.round(cvae_mean.flatten(), 2))

    # Diffusion
    diff_samples = predict(pose6d, mode="diffusion", device=args.device, n_samples=args.n_samples)
    diff_mean = np.mean(diff_samples, axis=0)
    print("Diffusion预测:", np.round(diff_mean.flatten(), 2))