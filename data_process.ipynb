{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3db6341e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8caacb9c", "metadata": {}, "outputs": [], "source": ["original_order = [\n", "    \"Shoulder_Y_<PERSON>\", \"Shoulder_<PERSON>_R\", \"Shoulder_Z_<PERSON>\",\n", "    \"<PERSON><PERSON>_R\", \"<PERSON>rist_Z_R\", \"Wrist_Y_R\", \"Wrist_X_R\",\n", "    \"Shoulder_<PERSON>_<PERSON>\", \"Shoulder_<PERSON>_L\", \"Shoulder_Z_<PERSON>\",\n", "    \"<PERSON><PERSON>_L\", \"<PERSON>rist_Z_L\", \"Wrist_Y_L\", \"Wrist_X_L\",\n", "    \"Neck_<PERSON>\", \"Neck_<PERSON>\", \"Waist_Z\", \"Waist_Y\", \"Lifting_Z\"\n", "]\n", "new_order = [\n", "    \"x_dir_joint\", \"y_dir_joint\", \"z_dir_joint\",\n", "    \"Lifting_Z\", \"Waist_Z\", \"Waist_Y\",\n", "    \"Shoulder_<PERSON>_<PERSON>\", \"Shoulder_<PERSON>_L\", \"Shoulder_Z_<PERSON>\",\n", "    \"<PERSON><PERSON>_L\", \"<PERSON>rist_Z_L\", \"Wrist_Y_L\", \"Wrist_X_L\",\n", "    \"Shoulder_Y_<PERSON>\", \"Shoulder_<PERSON>_R\", \"Shoulder_Z_<PERSON>\",\n", "    \"<PERSON><PERSON>_R\", \"<PERSON>rist_Z_R\", \"Wrist_Y_R\", \"Wrist_X_R\",\n", "]\n", "\n", "orig_idx = {name: i for i, name in enumerate(original_order)}\n", "\n", "new_indices = []\n", "for name in new_order:\n", "    if name in orig_idx:\n", "        new_indices.append(orig_idx[name])\n", "    else:\n", "        new_indices.append(None)  # 方向关节"]}, {"cell_type": "code", "execution_count": 3, "id": "33394de6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["拼接完成，保存为 all_data.npy，总形状: (5069, 20)\n"]}], "source": ["collected_data_dir = 'collected_data'\n", "npy_files = [os.path.join(collected_data_dir, f) for f in os.listdir(collected_data_dir) if f.endswith('.npy')]\n", "\n", "all_data = []\n", "for file in npy_files:\n", "    data = np.load(file)\n", "    if data.shape[1] != 19:\n", "        raise ValueError(f\"File {file} does not have 19 columns, shape: {data.shape}\")\n", "    n = data.shape[0]\n", "    new_data = np.zeros((n, 20), dtype=data.dtype)\n", "    for i, idx in enumerate(new_indices):\n", "        if idx is not None:\n", "            new_data[:, i] = data[:, idx]\n", "        # else: 保持为0\n", "    all_data.append(new_data)\n", "\n", "all_data = np.vstack(all_data)\n", "np.save('all_data.npy', all_data)\n", "print(f\"拼接完成，保存为 all_data.npy，总形状: {all_data.shape}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "8bd9b3a1", "metadata": {}, "outputs": [], "source": ["def map_data_to_q(data_row):\n", "    q = pin.neutral(model).copy()\n", "    q[0] = data_row[0]\n", "    q[1] = data_row[1]\n", "    q[2] = data_row[2]\n", "    q[3] = 0.0\n", "    q[4] = data_row[3]\n", "    q[5] = data_row[4]\n", "    q[6] = data_row[5]\n", "    q[7] = 0.0\n", "    q[8] = 0.0\n", "    q[9] = data_row[6]\n", "    q[10] = data_row[7]\n", "    q[11] = data_row[8]\n", "    q[12] = data_row[9]\n", "    q[13] = data_row[10]\n", "    q[14] = data_row[11]\n", "    q[15] = data_row[12]\n", "    q[16] = data_row[13]\n", "    q[17] = data_row[14]\n", "    q[18] = data_row[15]\n", "    q[19] = data_row[16]\n", "    q[20] = data_row[17]\n", "    q[21] = data_row[18]\n", "    q[22] = data_row[19]\n", "    return q"]}, {"cell_type": "code", "execution_count": 5, "id": "c1f1e9c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You can open the visualizer by visiting the following URL:\n", "http://127.0.0.1:7000/static/\n", "可视化已启动，请打开浏览器查看坐标系\n", "已添加关节位置标记，如果能看到绿色小球说明可视化正常工作\n"]}], "source": ["import pinocchio as pin\n", "from pinocchio.visualize import MeshcatVisualizer\n", "import numpy as np\n", "# from pinocchio.utils import rpy\n", "\n", "urdf_path = \"mm_real.urdf\"\n", "model = pin.buildModelFromUrdf(urdf_path)\n", "\n", "viz = MeshcatVisualizer(model)\n", "viz.init<PERSON><PERSON><PERSON>(open=True)\n", "\n", "print(\"可视化已启动，请打开浏览器查看坐标系\")\n", "\n", "# 手动添加关节坐标系\n", "data = model.createData()\n", "q = pin.neutral(model)\n", "pin.forwardKinematics(model, data, q)\n", "\n", "# 添加一些几何体来显示关节位置\n", "import meshcat.geometry as g\n", "\n", "\n", "# 为每个关节添加小球体来显示位置\n", "for i in range(len(data.oMi)):\n", "    if i > 0:  # 跳过根节点\n", "        pos = data.oMi[i].translation\n", "        viz.viewer[f\"joint_{i}\"].set_object(\n", "            g.<PERSON>(0.02),\n", "            g.MeshLambertMaterial(color=0x00ff00)\n", "        )\n", "        joint_transform = np.eye(4)\n", "        joint_transform[:3, 3] = pos\n", "        viz.viewer[f\"joint_{i}\"].set_transform(joint_transform)\n", "\n", "print(\"已添加关节位置标记，如果能看到绿色小球说明可视化正常工作\")"]}, {"cell_type": "code", "execution_count": 6, "id": "4abf4c7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 0 universe             nq=1 idx_q=-1\n", " 1 x_dir_joint          nq=1 idx_q=0\n", " 2 y_dir_joint          nq=1 idx_q=1\n", " 3 z_dir_joint          nq=2 idx_q=2\n", " 4 Lifting_Z            nq=1 idx_q=4\n", " 5 Waist_Z              nq=1 idx_q=5\n", " 6 Waist_Y              nq=1 idx_q=6\n", " 7 Neck_Z               nq=1 idx_q=7\n", " 8 Neck_Y               nq=1 idx_q=8\n", " 9 Shoulder_Y_L         nq=1 idx_q=9\n", "10 Shoulder_X_L         nq=1 idx_q=10\n", "11 Shoulder_Z_L         nq=1 idx_q=11\n", "12 Elbow_L              nq=1 idx_q=12\n", "13 Wrist_<PERSON>_L            nq=1 idx_q=13\n", "14 Wrist_Y_L            nq=1 idx_q=14\n", "15 Wrist_X_L            nq=1 idx_q=15\n", "16 Shoulder_Y_R         nq=1 idx_q=16\n", "17 Shoulder_X_R         nq=1 idx_q=17\n", "18 Shoulder_Z_R         nq=1 idx_q=18\n", "19 Elbow_R              nq=1 idx_q=19\n", "20 Wrist_Z_R            nq=1 idx_q=20\n", "21 Wrist_Y_R            nq=1 idx_q=21\n", "22 Wrist_X_R            nq=1 idx_q=22\n"]}], "source": ["for idx, joint in enumerate(model.joints):\n", "    print(f\"{idx:2d} {model.names[idx]:20s} nq={joint.nq} idx_q={joint.idx_q}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "5862f5b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(23,)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pin.neutral(model).copy().shape"]}, {"cell_type": "code", "execution_count": 8, "id": "d18aca7e", "metadata": {}, "outputs": [], "source": ["q_list = []\n", "for i, data_row in enumerate(all_data):\n", "    q = pin.neutral(model).copy()\n", "    q[16] = data_row[13] # Shoulder_Y_R\n", "    q[17] = data_row[14] # Shoulder_X_R\n", "    q[18] = data_row[15] # Shoulder_Z_R\n", "    q[19] = data_row[16] # Elbow_R\n", "    q[20] = data_row[17] # Wrist_Z_R\n", "    q[21] = data_row[18] # Wrist_Y_R\n", "    q[22] = data_row[19] # Wrist_X_R\n", "    q_list.append(q)\n", "\n", "q_array = np.array(q_list)"]}, {"cell_type": "code", "execution_count": 9, "id": "eb399a1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["前5个TCP_R位置：\n", "[[ 0.88006649 -0.19232831  1.33088885 -1.58839989  0.01621431  2.25346469]\n", " [ 0.8800671  -0.19233722  1.33089371 -1.58839936  0.01622897  2.25345041]\n", " [ 0.88006835 -0.19234139  1.33091391 -1.58842402  0.01626692  2.25343818]\n", " [ 0.88006576 -0.19231636  1.33089217 -1.58838931  0.01624237  2.25348865]\n", " [ 0.88006561 -0.1923179   1.33088914 -1.58839416  0.01622372  2.2534874 ]]\n"]}], "source": ["import pinocchio as pin\n", "\n", "urdf_path = \"mm_real.urdf\"  # 或你的实际路径\n", "model = pin.buildModelFromUrdf(urdf_path)\n", "data = model.createData()\n", "\n", "\n", "pose6d_list = []\n", "tcp_r_frame_id = model.getFrameId(\"TCP_R\")  # 确认你的URDF里末端执行器frame名为TCP_R\n", "\n", "for q in q_array:\n", "    pin.forwardKinematics(model, data, q)\n", "    pin.updateFramePlacements(model, data)\n", "    pose = data.oMf[tcp_r_frame_id]\n", "    xyz = pose.translation\n", "    rpy_angles = pin.rpy.matrixToRpy(pose.rotation)  # roll, pitch, yaw\n", "    pose6d = np.concatenate([xyz, rpy_angles])       # [x, y, z, roll, pitch, yaw]\n", "    pose6d_list.append(pose6d)\n", "\n", "pose6d_array = np.array(pose6d_list)  # shape: (N, 6)\n", "print(\"前5个TCP_R位置：\")\n", "print(pose6d_array[:5])"]}, {"cell_type": "code", "execution_count": 10, "id": "844b31a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["全局去重后剩余 823 个点\n", "已保存去重后的末端6维位姿到 filtered_pose6d_array.npy\n"]}], "source": ["import numpy as np\n", "\n", "# pose6d_array: shape (N, 6)\n", "threshold = 0.001\n", "keep_indices = []\n", "for i in range(len(pose6d_array)):\n", "    current = pose6d_array[i]\n", "    if all(np.linalg.norm(current - pose6d_array[j]) > threshold for j in keep_indices):\n", "        keep_indices.append(i)\n", "\n", "# 用keep_indices同步筛选q_array和pose6d_array\n", "filtered_pose6d_array = pose6d_array[keep_indices]\n", "filtered_q_array = q_array[keep_indices]\n", "filtered_all_data = all_data[keep_indices]\n", "\n", "np.save('filtered_pose6d_array.npy', filtered_pose6d_array)\n", "np.save('filtered_q_array_pinocchio.npy', filtered_q_array)\n", "np.save('filtered_q_array_origin.npy', filtered_all_data)\n", "\n", "print(f\"全局去重后剩余 {len(filtered_pose6d_array)} 个点\")\n", "np.save('filtered_pose6d_array.npy', filtered_pose6d_array)\n", "print('已保存去重后的末端6维位姿到 filtered_pose6d_array.npy')"]}, {"cell_type": "code", "execution_count": 11, "id": "65ef026c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最大绝对误差： 0.0\n", "✅ 重新正向运动学结果与去重时保存的pose完全一致，对应关系无误！\n"]}], "source": ["filtered_pose6d_array_check = []\n", "for q in filtered_q_array:\n", "    pin.forwardKinematics(model, data, q)\n", "    pin.updateFramePlacements(model, data)\n", "    pose = data.oMf[tcp_r_frame_id]\n", "    xyz = pose.translation\n", "    rpy_angles = pin.rpy.matrixToRpy(pose.rotation)\n", "    pose6d = np.concatenate([xyz, rpy_angles])\n", "    filtered_pose6d_array_check.append(pose6d)\n", "\n", "filtered_pose6d_array_check = np.array(filtered_pose6d_array_check)\n", "\n", "# 检查是否与原filtered_pose6d_array完全一致\n", "diff = np.abs(filtered_pose6d_array_check - filtered_pose6d_array)\n", "print(\"最大绝对误差：\", np.max(diff))\n", "if np.allclose(filtered_pose6d_array_check, filtered_pose6d_array, atol=1e-8):\n", "    print(\"✅ 重新正向运动学结果与去重时保存的pose完全一致，对应关系无误！\")\n", "else:\n", "    print(\"❌ 有不一致，请检查数据处理流程！\")"]}, {"cell_type": "code", "execution_count": 12, "id": "89b171c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(823, 20)\n"]}], "source": ["print(filtered_all_data.shape)"]}, {"cell_type": "code", "execution_count": 13, "id": "39471e70", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[13], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>lot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[1;32m      3\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n\u001b[1;32m      4\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(filtered_pose6d_array[:, \u001b[38;5;241m0\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mX\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'matplotlib'"]}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(filtered_pose6d_array[:, 0], label='X')\n", "plt.plot(filtered_pose6d_array[:, 1], label='Y')\n", "plt.plot(filtered_pose6d_array[:, 2], label='Z')\n", "plt.xlabel('time')\n", "plt.ylabel('position (m)')\n", "plt.title('tcp_r trajectory')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 可选：三维轨迹\n", "fig = plt.figure(figsize=(8, 8))\n", "ax = fig.add_subplot(111, projection='3d')\n", "ax.plot(filtered_pose6d_array[:, 0], filtered_pose6d_array[:, 1], filtered_pose6d_array[:, 2], label='tcp_r_array')\n", "ax.set_xlabel('X (m)')\n", "ax.set_ylabel('Y (m)')\n", "ax.set_zlabel('Z (m)')\n", "ax.set_title('tcp_r trajectory')\n", "ax.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "0d322788", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}