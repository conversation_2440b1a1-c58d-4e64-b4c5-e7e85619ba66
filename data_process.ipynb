import numpy as np
import os


original_order = [
    "Shoulder_Y_R", "Shoulder_X_R", "Shoulder_Z_R",
    "Elbow_R", "Wrist_Z_R", "Wrist_Y_R", "Wrist_X_R",
    "Shoulder_Y_L", "Shoulder_X_L", "Shoulder_Z_L",
    "Elbow_L", "Wrist_Z_L", "Wrist_Y_L", "Wrist_X_L",
    "Neck_Z", "Neck_Y", "Waist_Z", "Waist_Y", "Lifting_Z"
]
new_order = [
    "x_dir_joint", "y_dir_joint", "z_dir_joint",
    "Lifting_Z", "Waist_Z", "Waist_Y",
    "Shoulder_Y_L", "Shoulder_X_L", "Shoulder_Z_L",
    "Elbow_L", "Wrist_Z_L", "Wrist_Y_L", "Wrist_X_L",
    "Shoulder_Y_R", "Shoulder_X_R", "Shoulder_Z_R",
    "<PERSON><PERSON>_R", "Wrist_Z_R", "Wrist_Y_R", "Wrist_X_R",
]

orig_idx = {name: i for i, name in enumerate(original_order)}

new_indices = []
for name in new_order:
    if name in orig_idx:
        new_indices.append(orig_idx[name])
    else:
        new_indices.append(None)  # 方向关节

collected_data_dir = 'collected_data'
npy_files = [os.path.join(collected_data_dir, f) for f in os.listdir(collected_data_dir) if f.endswith('.npy')]

all_data = []
for file in npy_files:
    data = np.load(file)
    if data.shape[1] != 19:
        raise ValueError(f"File {file} does not have 19 columns, shape: {data.shape}")
    n = data.shape[0]
    new_data = np.zeros((n, 20), dtype=data.dtype)
    for i, idx in enumerate(new_indices):
        if idx is not None:
            new_data[:, i] = data[:, idx]
        # else: 保持为0
    all_data.append(new_data)

all_data = np.vstack(all_data)
np.save('all_data.npy', all_data)
print(f"拼接完成，保存为 all_data.npy，总形状: {all_data.shape}")

def map_data_to_q(data_row):
    q = pin.neutral(model).copy()
    q[0] = data_row[0]
    q[1] = data_row[1]
    q[2] = data_row[2]
    q[3] = 0.0
    q[4] = data_row[3]
    q[5] = data_row[4]
    q[6] = data_row[5]
    q[7] = 0.0
    q[8] = 0.0
    q[9] = data_row[6]
    q[10] = data_row[7]
    q[11] = data_row[8]
    q[12] = data_row[9]
    q[13] = data_row[10]
    q[14] = data_row[11]
    q[15] = data_row[12]
    q[16] = data_row[13]
    q[17] = data_row[14]
    q[18] = data_row[15]
    q[19] = data_row[16]
    q[20] = data_row[17]
    q[21] = data_row[18]
    q[22] = data_row[19]
    return q

import pinocchio as pin
from pinocchio.visualize import MeshcatVisualizer
import numpy as np
# from pinocchio.utils import rpy

urdf_path = "mm_real.urdf"
model = pin.buildModelFromUrdf(urdf_path)

viz = MeshcatVisualizer(model)
viz.initViewer(open=True)

print("可视化已启动，请打开浏览器查看坐标系")

# 手动添加关节坐标系
data = model.createData()
q = pin.neutral(model)
pin.forwardKinematics(model, data, q)

# 添加一些几何体来显示关节位置
import meshcat.geometry as g


# 为每个关节添加小球体来显示位置
for i in range(len(data.oMi)):
    if i > 0:  # 跳过根节点
        pos = data.oMi[i].translation
        viz.viewer[f"joint_{i}"].set_object(
            g.Sphere(0.02),
            g.MeshLambertMaterial(color=0x00ff00)
        )
        joint_transform = np.eye(4)
        joint_transform[:3, 3] = pos
        viz.viewer[f"joint_{i}"].set_transform(joint_transform)

print("已添加关节位置标记，如果能看到绿色小球说明可视化正常工作")

for idx, joint in enumerate(model.joints):
    print(f"{idx:2d} {model.names[idx]:20s} nq={joint.nq} idx_q={joint.idx_q}")

pin.neutral(model).copy().shape

q_list = []
for i, data_row in enumerate(all_data):
    q = pin.neutral(model).copy()
    q[16] = data_row[13] # Shoulder_Y_R
    q[17] = data_row[14] # Shoulder_X_R
    q[18] = data_row[15] # Shoulder_Z_R
    q[19] = data_row[16] # Elbow_R
    q[20] = data_row[17] # Wrist_Z_R
    q[21] = data_row[18] # Wrist_Y_R
    q[22] = data_row[19] # Wrist_X_R
    q_list.append(q)

q_array = np.array(q_list)

import pinocchio as pin

urdf_path = "mm_real.urdf"  # 或你的实际路径
model = pin.buildModelFromUrdf(urdf_path)
data = model.createData()


pose6d_list = []
tcp_r_frame_id = model.getFrameId("TCP_R")  # 确认你的URDF里末端执行器frame名为TCP_R

for q in q_array:
    pin.forwardKinematics(model, data, q)
    pin.updateFramePlacements(model, data)
    pose = data.oMf[tcp_r_frame_id]
    xyz = pose.translation
    rpy_angles = pin.rpy.matrixToRpy(pose.rotation)  # roll, pitch, yaw
    pose6d = np.concatenate([xyz, rpy_angles])       # [x, y, z, roll, pitch, yaw]
    pose6d_list.append(pose6d)

pose6d_array = np.array(pose6d_list)  # shape: (N, 6)
print("前5个TCP_R位置：")
print(pose6d_array[:5])

import numpy as np

# pose6d_array: shape (N, 6)
threshold = 0.001
keep_indices = []
for i in range(len(pose6d_array)):
    current = pose6d_array[i]
    if all(np.linalg.norm(current - pose6d_array[j]) > threshold for j in keep_indices):
        keep_indices.append(i)

# 用keep_indices同步筛选q_array和pose6d_array
filtered_pose6d_array = pose6d_array[keep_indices]
filtered_q_array = q_array[keep_indices]
filtered_all_data = all_data[keep_indices]

np.save('filtered_pose6d_array.npy', filtered_pose6d_array)
np.save('filtered_q_array_pinocchio.npy', filtered_q_array)
np.save('filtered_q_array_origin.npy', filtered_all_data)

print(f"全局去重后剩余 {len(filtered_pose6d_array)} 个点")
np.save('filtered_pose6d_array.npy', filtered_pose6d_array)
print('已保存去重后的末端6维位姿到 filtered_pose6d_array.npy')

filtered_pose6d_array_check = []
for q in filtered_q_array:
    pin.forwardKinematics(model, data, q)
    pin.updateFramePlacements(model, data)
    pose = data.oMf[tcp_r_frame_id]
    xyz = pose.translation
    rpy_angles = pin.rpy.matrixToRpy(pose.rotation)
    pose6d = np.concatenate([xyz, rpy_angles])
    filtered_pose6d_array_check.append(pose6d)

filtered_pose6d_array_check = np.array(filtered_pose6d_array_check)

# 检查是否与原filtered_pose6d_array完全一致
diff = np.abs(filtered_pose6d_array_check - filtered_pose6d_array)
print("最大绝对误差：", np.max(diff))
if np.allclose(filtered_pose6d_array_check, filtered_pose6d_array, atol=1e-8):
    print("✅ 重新正向运动学结果与去重时保存的pose完全一致，对应关系无误！")
else:
    print("❌ 有不一致，请检查数据处理流程！")

print(filtered_all_data.shape)

import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
plt.plot(filtered_pose6d_array[:, 0], label='X')
plt.plot(filtered_pose6d_array[:, 1], label='Y')
plt.plot(filtered_pose6d_array[:, 2], label='Z')
plt.xlabel('time')
plt.ylabel('position (m)')
plt.title('tcp_r trajectory')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()

# 可选：三维轨迹
fig = plt.figure(figsize=(8, 8))
ax = fig.add_subplot(111, projection='3d')
ax.plot(filtered_pose6d_array[:, 0], filtered_pose6d_array[:, 1], filtered_pose6d_array[:, 2], label='tcp_r_array')
ax.set_xlabel('X (m)')
ax.set_ylabel('Y (m)')
ax.set_zlabel('Z (m)')
ax.set_title('tcp_r trajectory')
ax.legend()
plt.show()

