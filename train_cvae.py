import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import TensorDataset, DataLoader
import os
from utils import normalizer

class CVAE(nn.Module):
    def __init__(self, pose_dim=6, joint_dim=7, z_dim=8, hidden_dim=128):
        super().__init__()
        # Encoder: [pose, joint] -> z
        self.encoder = nn.Sequential(
            nn.Linear(pose_dim + joint_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        self.enc_mu = nn.Linear(hidden_dim, z_dim)
        self.enc_logvar = nn.Linear(hidden_dim, z_dim)
        # Decoder: [pose, z] -> joint
        self.decoder = nn.Sequential(
            nn.Linear(pose_dim + z_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, joint_dim)
        )

    def encode(self, pose, joint):
        h = self.encoder(torch.cat([pose, joint], dim=1))
        mu = self.enc_mu(h)
        logvar = self.enc_logvar(h)
        return mu, logvar

    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, pose, z):
        return self.decoder(torch.cat([pose, z], dim=1))

    def forward(self, pose, joint):
        mu, logvar = self.encode(pose, joint)
        z = self.reparameterize(mu, logvar)
        recon_joint = self.decode(pose, z)
        return recon_joint, mu, logvar

# 损失函数
def cvae_loss(recon_joint, joint, mu, logvar):
    recon_loss = F.mse_loss(recon_joint, joint, reduction='mean')
    # KL散度
    kl = -0.5 * torch.mean(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl, recon_loss, kl

if __name__ == "__main__":
    # 1. 读取数据
    pose6d = np.load('filtered_pose6d_array.npy')  # (N, 6)
    q_array = np.load('filtered_q_array_origin.npy')  # (N, 20)
    right_arm_indices = list(range(13, 20))  # 13-19, 共7个关节
    right_arm_q = q_array[:, right_arm_indices]  # (N, 7)

    # 2. 转为Tensor
    X = torch.from_numpy(pose6d.astype('float32'))
    y = torch.from_numpy(right_arm_q.astype('float32'))

    norm = normalizer.load('checkpoints/normalizer.pkl')
    X = torch.from_numpy(norm.x)
    y = torch.from_numpy(norm.y)

    # 3. 划分训练/验证集
    N = X.shape[0]
    split = int(N * 0.8)
    X_train, X_val = X[:split], X[split:]
    y_train, y_val = y[:split], y[split:]
    train_ds = TensorDataset(X_train, y_train)
    val_ds = TensorDataset(X_val, y_val)
    train_loader = DataLoader(train_ds, batch_size=512, shuffle=True)
    val_loader = DataLoader(val_ds, batch_size=512)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CVAE().to(device)
    optimizer = torch.optim.SGD(model.parameters(), lr=1e-5)

    n_epochs = 50000
    for epoch in range(n_epochs):
        model.train()
        total_loss = 0
        for xb, yb in train_loader:
            xb, yb = xb.to(device), yb.to(device)
            recon, mu, logvar = model(xb, yb)
            loss, recon_loss, kl = cvae_loss(recon, yb, mu, logvar)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            total_loss += loss.item() * xb.size(0)
        total_loss /= len(train_loader.dataset)
        model.eval()

        val_loss = 0
        val_recon_loss = 0
        val_kl = 0
        with torch.no_grad():
            for xb, yb in val_loader:
                xb, yb = xb.to(device), yb.to(device)
                recon, mu, logvar = model(xb, yb)
                loss, recon_loss, kl = cvae_loss(recon, yb, mu, logvar)
                val_loss += loss.item() * xb.size(0)
                val_recon_loss += recon_loss.item() * xb.size(0)
                val_kl += kl.item() * xb.size(0)
        val_loss /= len(val_loader.dataset)
        val_recon_loss /= len(val_loader.dataset)
        val_kl /= len(val_loader.dataset)

        if (epoch+1) % 500 == 0 or epoch == 0:
            print(f"Epoch {epoch+1}, Train Loss: {total_loss:.6f}, Val Loss: {val_loss:.6f}, Val Recon: {val_recon_loss:.6f}, Val KL: {val_kl:.6f}")
    
    os.makedirs('checkpoints/cvae', exist_ok=True)
    torch.save(model.state_dict(), 'checkpoints/cvae/model.pt')

    # 6. 推理示例
    model.eval()
    n_show = 5  # 多预测几组

    with torch.no_grad():
        for i in range(n_show):
            test_pose = X_val[i:i+1].to(device)
            pred_q_norm = model(test_pose)
            # 反归一化
            pred_q = norm.denormalize(pred_q_norm.cpu().numpy(), is_train_x=False)
            true_q = norm.denormalize(y_val[i:i+1].cpu().numpy(), is_train_x=False)
            print(f"样本 {i}:")
            print("  预测的右臂7关节角度：", np.round(pred_q, 3))
            print("  真实的右臂7关节角度：", np.round(true_q, 3))