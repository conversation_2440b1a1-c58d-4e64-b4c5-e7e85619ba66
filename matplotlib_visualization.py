#!/usr/bin/env python3
"""
Use matplotlib to display robot model visualization directly in Jupyter notebook
Alternative to meshcat, no need to open web browser
"""

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
import pinocchio as pin

def setup_matplotlib():
    """Setup matplotlib for notebook display"""
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10
    print("matplotlib setup completed")

def visualize_robot_3d(model, data, q=None, title="Robot Model Visualization"):
    """Visualize robot model in 3D plot"""
    if q is None:
        q = pin.neutral(model)
    
    # Compute forward kinematics
    pin.forwardKinematics(model, data, q)
    
    # Create 3D plot
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Collect all joint positions
    joint_positions = []
    joint_names = []
    
    for i in range(len(data.oMi)):
        if i > 0:  # Skip root node
            pos = data.oMi[i].translation
            joint_positions.append(pos)
            joint_names.append(model.names[i])
    
    joint_positions = np.array(joint_positions)
    
    # Plot joint positions only (no connecting lines)
    ax.scatter(joint_positions[:, 0], joint_positions[:, 1], joint_positions[:, 2], 
               c='red', s=100, alpha=0.7, label='Joint Positions')
    
    # Set axis labels
    ax.set_xlabel('X Axis')
    ax.set_ylabel('Y Axis')
    ax.set_zlabel('Z Axis')
    ax.set_title(title)
    
    # Set axis limits
    margin = 0.5
    ax.set_xlim([joint_positions[:, 0].min() - margin, joint_positions[:, 0].max() + margin])
    ax.set_ylim([joint_positions[:, 1].min() - margin, joint_positions[:, 1].max() + margin])
    ax.set_zlim([joint_positions[:, 2].min() - margin, joint_positions[:, 2].max() + margin])
    
    ax.legend()
    plt.tight_layout()
    plt.show()
    
    return joint_positions, joint_names

def visualize_trajectory(pose6d_array, title="TCP Trajectory Visualization"):
    """Visualize TCP trajectory"""
    plt.figure(figsize=(15, 10))
    
    # Position trajectory
    plt.subplot(2, 3, 1)
    plt.plot(pose6d_array[:, 0], label='X', linewidth=2)
    plt.plot(pose6d_array[:, 1], label='Y', linewidth=2)
    plt.plot(pose6d_array[:, 2], label='Z', linewidth=2)
    plt.xlabel('Time Step')
    plt.ylabel('Position (m)')
    plt.title('TCP Position Trajectory')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Orientation trajectory
    plt.subplot(2, 3, 2)
    plt.plot(pose6d_array[:, 3], label='Roll', linewidth=2)
    plt.plot(pose6d_array[:, 4], label='Pitch', linewidth=2)
    plt.plot(pose6d_array[:, 5], label='Yaw', linewidth=2)
    plt.xlabel('Time Step')
    plt.ylabel('Angle (rad)')
    plt.title('TCP Orientation Trajectory')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3D trajectory
    ax = plt.subplot(2, 3, 3, projection='3d')
    ax.plot(pose6d_array[:, 0], pose6d_array[:, 1], pose6d_array[:, 2], 
            label='TCP Trajectory', linewidth=2, color='red')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('TCP 3D Trajectory')
    ax.legend()
    
    # Position distribution
    plt.subplot(2, 3, 4)
    plt.scatter(pose6d_array[:, 0], pose6d_array[:, 1], alpha=0.6, s=10)
    plt.xlabel('X Position (m)')
    plt.ylabel('Y Position (m)')
    plt.title('XY Plane Position Distribution')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 5)
    plt.scatter(pose6d_array[:, 0], pose6d_array[:, 2], alpha=0.6, s=10)
    plt.xlabel('X Position (m)')
    plt.ylabel('Z Position (m)')
    plt.title('XZ Plane Position Distribution')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 3, 6)
    plt.scatter(pose6d_array[:, 1], pose6d_array[:, 2], alpha=0.6, s=10)
    plt.xlabel('Y Position (m)')
    plt.ylabel('Z Position (m)')
    plt.title('YZ Plane Position Distribution')
    plt.grid(True, alpha=0.3)
    
    plt.suptitle(title, fontsize=16)
    plt.tight_layout()
    plt.show()

def main():
    """Main function to demonstrate matplotlib visualization"""
    print("=== matplotlib Robot Visualization Demo ===")
    
    # Setup matplotlib
    setup_matplotlib()
    
    # Load robot model
    urdf_path = "mm_real.urdf"
    model = pin.buildModelFromUrdf(urdf_path)
    data = model.createData()
    
    # Visualize robot model with spheres (joint positions)
    print("\n1. Visualizing robot model with joint positions...")
    joint_positions, joint_names = visualize_robot_3d(model, data, title="Robot Model Visualization")
    print(f"Successfully visualized robot model with {len(joint_positions)} joint positions")
    print("Joint names:", joint_names)
    

    pose6d_array = np.load('filtered_pose6d_array.npy')
    print(f"\n2. Visualizing TCP trajectory...")
    print(f"Loaded {len(pose6d_array)} pose data points")
    visualize_trajectory(pose6d_array, "TCP Trajectory Visualization")

if __name__ == "__main__":
    main() 