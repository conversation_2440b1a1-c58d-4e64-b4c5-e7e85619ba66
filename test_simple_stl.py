#!/usr/bin/env python3
"""
Test simple STL file loading
"""

import struct
import numpy as np
import os

def load_stl_file_simple(filepath):
    """Simple STL file loader"""
    try:
        with open(filepath, 'rb') as f:
            # Skip header (80 bytes)
            f.read(80)
            
            # Read number of triangles (4 bytes)
            num_triangles = struct.unpack('<I', f.read(4))[0]
            
            vertices = []
            faces = []
            
            for i in range(num_triangles):
                # Read normal (12 bytes) and vertices (36 bytes)
                data = f.read(48)
                if len(data) != 48:
                    break
                    
                # Parse vertices (3 floats each)
                v1 = struct.unpack('<fff', data[12:24])
                v2 = struct.unpack('<fff', data[24:36])
                v3 = struct.unpack('<fff', data[36:48])
                
                # Add vertices
                start_idx = len(vertices)
                vertices.extend([v1, v2, v3])
                faces.append([start_idx, start_idx + 1, start_idx + 2])
            
            return np.array(vertices), np.array(faces)
    except Exception as e:
        print(f"Error loading STL file {filepath}: {e}")
        return None, None

def test_stl_loading():
    """Test STL file loading"""
    mesh_dir = "/home/<USER>/workspace/mpc_wheel_manip_ws/src/ocs2/ocs2_robotic_assets/resources/wa1_mm/meshes"
    
    if not os.path.exists(mesh_dir):
        print(f"Mesh directory not found: {mesh_dir}")
        return
    
    # Test a few STL files
    test_files = ['TORSO.STL', 'HEAD.STL', 'BASE.STL']
    
    for test_file in test_files:
        mesh_path = os.path.join(mesh_dir, test_file)
        print(f"\nTesting: {test_file}")
        print(f"File exists: {os.path.exists(mesh_path)}")
        
        if os.path.exists(mesh_path):
            vertices, faces = load_stl_file_simple(mesh_path)
            if vertices is not None:
                print(f"Successfully loaded {test_file}")
                print(f"  Vertices: {len(vertices)}")
                print(f"  Faces: {len(faces)}")
                print(f"  Vertex bounds: {vertices.min(axis=0)} to {vertices.max(axis=0)}")
            else:
                print(f"Failed to load {test_file}")

if __name__ == "__main__":
    test_stl_loading() 