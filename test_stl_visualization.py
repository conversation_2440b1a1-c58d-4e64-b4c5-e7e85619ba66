#!/usr/bin/env python3
"""
Test script to verify STL visualization functionality
"""

import sys
import os

# Test if numpy-stl is available
try:
    from stl import mesh
    print("✓ numpy-stl is available")
    STL_AVAILABLE = True
except ImportError:
    print("✗ numpy-stl is not available. Install with: pip install numpy-stl")
    STL_AVAILABLE = False

# Test if STL files exist
meshes_path = "/home/<USER>/workspace/mpc_wheel_manip_ws/src/ocs2/ocs2_robotic_assets/resources/wa1_mm/meshes"
stl_files = [
    "SHOULDER_R.STL",
    "UPPERARM_R.STL", 
    "FOREARM_R.STL",
    "WRIST_REVOLUTE_R.STL",
    "WRIST_UPDOWN_R.STL", 
    "WRIST_FLANGE_R.STL"
]

print(f"\nChecking STL files in: {meshes_path}")
for stl_file in stl_files:
    stl_path = os.path.join(meshes_path, stl_file)
    if os.path.exists(stl_path):
        print(f"✓ {stl_file} exists")
        if STL_AVAILABLE:
            try:
                stl_mesh = mesh.Mesh.from_file(stl_path)
                print(f"  - Successfully loaded, {len(stl_mesh.vectors)} triangles")
            except Exception as e:
                print(f"  - Error loading: {e}")
    else:
        print(f"✗ {stl_file} not found")

# Test the main visualization
if STL_AVAILABLE and os.path.exists(meshes_path):
    print("\n" + "="*50)
    print("Running main visualization test...")
    try:
        from matplotlib_visualization import main
        main()
    except Exception as e:
        print(f"Error running main visualization: {e}")
        import traceback
        traceback.print_exc()
else:
    print("\nSkipping main visualization test due to missing dependencies or files")
