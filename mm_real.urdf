<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-1-g15f4949  Build Version: 1.6.7594.29634
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="wa1_mm">
  
  <link name="root"/>
  <joint name="x_dir_joint" type="prismatic">
    <parent link="root"/>
    <child link="x_dir_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0"/>
    <axis xyz="1 0 0"/>
    <limit effort="1500" lower="-100" upper="100" velocity="5"/>
    <!-- <limit effort="100" lower="-100" upper="100" velocity="50"/> -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="x_dir_link">
    <inertial>
      <!-- <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/> -->
      <mass value="1.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0E-2" ixy="0.0" ixz="0.0" iyy="1.0E-2" iyz="0.0" izz="1.0E-2"/>
    </inertial>
  </link>
  <joint name="y_dir_joint" type="prismatic">
    <parent link="x_dir_link"/>
    <child link="y_dir_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500" lower="-100" upper="100" velocity="5"/>
    <!-- <limit effort="100" lower="-100" upper="100" velocity="50"/> -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="y_dir_link">
    <inertial>
      <!-- <mass value="0.01"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/> -->
      <mass value="1.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0E-2" ixy="0.0" ixz="0.0" iyy="1.0E-2" iyz="0.0" izz="1.0E-2"/>
    </inertial>
  </link>
  <joint name="z_dir_joint" type="continuous">
    <parent link="y_dir_link"/>
    <child link="current_footprint"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0"/>
    <axis xyz="0 0 1"/>
    <!-- <limit effort="100" velocity="50" lower="-6283.185307179586" upper="6283.185307179586"/> -->
    <limit effort="1500" velocity="5"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>

  <!-- <link name="current_footprint">
    <inertial>
      <origin
        xyz="2.51361431669039E-15 -1.59594559789866E-15 4.99999999998835E-05"
        rpy="0 0 0" />
      <mass
        value="6.28318530717892E-09" />
      <inertia
        ixx="9.16297857296993E-18"
        ixy="7.65809820955319E-47"
        ixz="1.60410529452E-32"
        iyy="9.16297857296993E-18"
        iyz="-6.53538616176467E-33"
        izz="7.8539816339715E-18" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/Ground.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.615686274509804 0.643137254901961 0.674509803921569 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <box size="0.1 0.1 0.1" />  
      </geometry>
    </collision>
  </link> -->
  <link name="current_footprint">
    <inertial>
      <!-- <mass value="0.1"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0" ixy="0.0" ixz="0.0" iyy="1.0" iyz="0.0" izz="1.0"/> -->
      <mass value="1.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1.0E-2" ixy="0.0" ixz="0.0" iyy="1.0E-2" iyz="0.0" izz="1.0E-2"/>
    </inertial>
  </link>
  <joint name="current_footprint_joint" type="fixed"> 
    <parent link="current_footprint"/>
    <child link="BASE"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.25"/>
  </joint>

  <link
    name="BASE">
    <inertial>
      <origin
        xyz="0.015476210305516 -4.46434734940604E-05 -0.0119944975278186"
        rpy="0 0 0" />
      <mass
        value="87.7232900770824" />
      <inertia
        ixx="0.800063847175825"
        ixy="0.000600880540934939"
        ixz="-0.0303843175126581"
        iyy="1.30694062885649"
        iyz="3.78247110320921E-05"
        izz="1.92675546351458" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/BASE.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.752941176470588 0.752941176470588 0.752941176470588 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.11"
        rpy="0 0 0" />
      <geometry>
        <box size="0.83 0.54 0.2745" />
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/BASE.STL" /> -->
      </geometry>
    </collision>
  </link>


  <link
    name="BODY">
    <inertial>
      <origin
        xyz="0.00288515407000706 -0.000274262082414697 -0.0639800777728884"
        rpy="0 0 0" />
      <mass
        value="6.83686735817836" />
      <inertia
        ixx="0.0473155162186864"
        ixy="6.84137666493064E-06"
        ixz="0.000686533857200616"
        iyy="0.046367721946548"
        iyz="7.1550967355852E-07"
        izz="0.00876267489881572" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/BODY.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.698039215686274 0.698039215686274 0.698039215686274 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.24"
        rpy="0 0 0" />
      <geometry>
        <box size="0.18 0.21 0.6" />
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/BODY.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Lifting_Z"
    type="prismatic">
    <origin
      xyz="0.30826 0.00025287 0.492"
      rpy="0 0 0" />
    <parent
      link="BASE" />
    <child
      link="BODY" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="0.16"
      effort="1000.0"
      velocity="0.05" />
  </joint>
 
  <link
    name="TORSO">
    <inertial>
      <origin
        xyz="1.71179814920652E-05 -0.00954679641927567 0.0744881951078242"
        rpy="0 0 0" />
      <mass
        value="5.10419871079781" />
      <inertia
        ixx="0.0103016172735309"
        ixy="-3.52772227865373E-06"
        ixz="-2.26208164748583E-06"
        iyy="0.00938436782353615"
        iyz="0.000359361606953714"
        izz="0.00998102999235041" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/TORSO.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
  </link>
  <joint
    name="Waist_Z"
    type="revolute">
    <origin
      xyz="0.0017351 -0.00025287 0.1325"
      rpy="0 0 0" />
    <parent
      link="BODY" />
    <child
      link="TORSO" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-2.0944"
      upper="3.2289"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="CHEST">
    <inertial>
      <origin
        xyz="-0.00229059338543275 -8.95675431315524E-05 0.272936133384229"
        rpy="0 0 0" />
      <mass
        value="10.6444482726447" />
      <inertia
        ixx="0.071142486992484"
        ixy="-0.000146364321575172"
        ixz="0.00056644871062178"
        iyy="0.043911081966"
        iyz="-0.000714928408375953"
        izz="0.0501436760104272" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/CHEST.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0.2"
        rpy="0 0 0" />
      <geometry>
        <box size="0.24 0.28 0.54"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/CHEST.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Waist_Y"
    type="revolute">
    <origin
      xyz="0 0 0.083"
      rpy="0 0 0" />
    <parent
      link="TORSO" />
    <child
      link="CHEST" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.523599"
      upper="0.7854"
      effort="1000.0"
      velocity="0.5" />
  </joint>

  <link
    name="SCAPULA_L">
    <inertial>
      <origin
        xyz="-0.000402154071813876 0.0702564201969916 -9.26935654392835E-05"
        rpy="0 0 0" />
      <mass
        value="1.11868522067552" />
      <inertia
        ixx="0.00101092349325568"
        ixy="-3.37777182972877E-05"
        ixz="-2.13709469327204E-06"
        iyy="0.00117731995739396"
        iyz="-4.45172716490175E-06"
        izz="0.00124017475611158" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SCAPULA_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.341176470588235 1 0.768627450980392 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SCAPULA_L.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="Shoulder_Y_L"
    type="revolute">
    <origin
      xyz="0.0074317 0.16387 0.39"
      rpy="0 0 -0.523599" />
    <parent
      link="CHEST" />
    <child
      link="SCAPULA_L" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-4.0143"
      upper="1.2217"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="SHOULDER_L">
    <inertial>
      <origin
        xyz="0.0216444992766726 0.00138565283790948 -0.0364115468735173"
        rpy="0 0 0" />
      <mass
        value="0.241263307747323" />
      <inertia
        ixx="0.000357939636654127"
        ixy="1.12609050387783E-05"
        ixz="-0.000118810132022054"
        iyy="0.000434380749920304"
        iyz="1.28612935371954E-05"
        izz="0.000360685747206799" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SHOULDER_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.07"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SHOULDER_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Shoulder_X_L"
    type="revolute">
    <origin
      xyz="0 0.075202 0"
      rpy="-0.349066 0 0" />
    <parent
      link="SCAPULA_L" />
    <child
      link="SHOULDER_L" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-0.034907"
      upper="3.1067"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="UPPERARM_L">
    <inertial>
      <origin
        xyz="-0.000573026570713286 0.000195790043335614 -0.0734525385752665"
        rpy="0 0 0" />
      <mass
        value="1.68415092736726" />
      <inertia
        ixx="0.00185774791077167"
        ixy="2.88116695933435E-08"
        ixz="-5.82127260358104E-06"
        iyy="0.00192677019199505"
        iyz="-3.87372237047266E-06"
        izz="0.000957785091050869" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/UPPERARM_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.0431372549019608 1 0.909803921568627 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.045"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.045" length="0.12"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/UPPERARM_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Shoulder_Z_L"
    type="revolute">
    <origin
      xyz="0 0 -0.0845"
      rpy="0 0 0" />
    <parent
      link="SHOULDER_L" />
    <child
      link="UPPERARM_L" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.1067"
      upper="3.1067"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="FOREARM_L">
    <inertial>
      <origin
        xyz="-0.00294114388574079 -0.00206446181961117 -0.0303479902300976"
        rpy="0 0 0" />
      <mass
        value="0.209947259258732" />
      <inertia
        ixx="0.000243843527461623"
        ixy="-6.4618692939534E-07"
        ixz="-1.35635533859053E-06"
        iyy="0.000196480540514592"
        iyz="-5.48672673991314E-05"
        izz="0.000233183026162112" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/FOREARM_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.06"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/FOREARM_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Elbow_L"
    type="revolute">
    <origin
      xyz="0 0 -0.1555"
      rpy="0 0 0" />
    <parent
      link="UPPERARM_L" />
    <child
      link="FOREARM_L" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.0944"
      upper="0.034907"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_REVOLUTE_L">
    <inertial>
      <origin
        xyz="-2.37664147301198E-06 0.00103312196043731 -0.0647879371733098"
        rpy="0 0 0" />
      <mass
        value="1.37743611612968" />
      <inertia
        ixx="0.00107361081301972"
        ixy="6.61261254716076E-08"
        ixz="1.36780919173371E-06"
        iyy="0.00109357892913548"
        iyz="6.34451093107546E-06"
        izz="0.000919967511495929" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_REVOLUTE_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.035"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.045" length="0.11"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_REVOLUTE_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_Z_L"
    type="revolute">
    <origin
      xyz="0 0 -0.0685"
      rpy="0 0 0" />
    <parent
      link="FOREARM_L" />
    <child
      link="WRIST_REVOLUTE_L" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.1067"
      upper="3.1067"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_UPDOWN_L">
    <inertial>
      <origin
        xyz="0.00178189890046337 0.000920112944104046 -0.0499206046896362"
        rpy="0 0 0" />
      <mass
        value="0.374884411618797" />
      <inertia
        ixx="0.000182011993931924"
        ixy="3.4037762822211E-06"
        ixz="8.02135726165362E-06"
        iyy="0.000180886471126199"
        iyz="-3.11098701097645E-05"
        izz="0.000143157751809596" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_UPDOWN_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.05"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_UPDOWN_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_Y_L"
    type="revolute">
    <origin
      xyz="0 0 -0.1385"
      rpy="0 0 0" />
    <parent
      link="WRIST_REVOLUTE_L" />
    <child
      link="WRIST_UPDOWN_L" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.5708"
      upper="1.5708"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_FLANGE_L">
    <inertial>
      <origin
        xyz="3.73989633467575E-05 0.000257764411103201 -0.0220500465635486"
        rpy="0 0 0" />
      <mass
        value="0.0624456934078318" />
      <inertia
        ixx="3.13314400252939E-05"
        ixy="1.74088891885927E-07"
        ixz="-9.99258191775104E-06"
        iyy="4.02440166453827E-05"
        iyz="3.69099231937431E-07"
        izz="3.08647353281775E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_FLANGE_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>  <!--存疑-->
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.04"/>
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_X_L"
    type="revolute">
    <origin
      xyz="0 0 -0.063"
      rpy="0 0 0" />
    <parent
      link="WRIST_UPDOWN_L" />
    <child
      link="WRIST_FLANGE_L" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.5708"
      upper="1.5708"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="TCP_L">
    <inertial>
      <origin
        xyz="0.000707099107293774 -0.000327578936353823 5.75097931809765E-06"
        rpy="0 0 0" />
      <mass
        value="0.0211623513252468" />
      <inertia
        ixx="2.79714723958179E-06"
        ixy="-2.42348416778525E-09"
        ixz="5.84678050008106E-10"
        iyy="2.82472045334378E-06"
        iyz="-1.22133918327192E-10"
        izz="5.43098748888683E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/TCP_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>  <!--存疑-->
      <origin
        xyz="0 0 -0.01"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.025" length="0.06"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/TCP_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Tcp_L"
    type="fixed">
    <origin
      xyz="0 0 -0.060997"
      rpy="0 0 0" />
    <parent
      link="WRIST_FLANGE_L" />
    <child
      link="TCP_L" />
    <axis
      xyz="0 0 0" />
  </joint>


  <!-- <link name="CatchPoint_L" />
  <joint name="catchpoint_L" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.025 -0.10"/>
    <parent link="TCP_L"/>
    <child link="CatchPoint_L"/>
  </joint> -->



  <link
    name="HAND_L">
    <inertial>
      <origin
        xyz="-0.00762321589853332 0.00305762711382171 0.0699751646532949"
        rpy="0 0 0" />
      <mass
        value="0.598867296054325" />
      <inertia
        ixx="0.000272162524249752"
        ixy="4.71394989754277E-05"
        ixz="3.77231652071565E-05"
        iyy="0.00044407296273663"
        iyz="-1.92574134112813E-05"
        izz="0.000248252963380286" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HAND_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0.02 0.01 0.12"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.08" />
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HAND_L.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Hand_L"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="3.1416 0 3.1416" />
    <parent
      link="TCP_L" />
    <child
      link="HAND_L" />
    <axis
      xyz="0 0 0" />
  </joint>



  
  <link
    name="SCAPULA_R">
    <inertial>
      <origin
        xyz="-0.000402150397512707 -0.070256286158244 -9.26357958517254E-05"
        rpy="0 0 0" />
      <mass
        value="1.11869314715603" />
      <inertia
        ixx="0.00101092896350837"
        ixy="3.37783558805185E-05"
        ixz="-2.14467612186563E-06"
        iyy="0.00117732650962382"
        iyz="4.40451051270008E-06"
        izz="0.00124017745981041" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SCAPULA_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SCAPULA_R.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="Shoulder_Y_R"
    type="revolute">
    <origin
      xyz="0.0074317 -0.16387 0.39"
      rpy="0 0 0.523599" />
    <parent
      link="CHEST" />
    <child
      link="SCAPULA_R" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-4.0143"
      upper="1.2217"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="SHOULDER_R">
    <inertial>
      <origin
        xyz="0.0216434594814481 -0.0013752300838189 -0.0364151172922005"
        rpy="0 0 0" />
      <mass
        value="0.241073510679558" />
      <inertia
        ixx="0.000357861834608315"
        ixy="-1.13634074100295E-05"
        ixz="-0.000118762274128345"
        iyy="0.00043414822356251"
        iyz="-1.2939797759082E-05"
        izz="0.000360485550917744" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SHOULDER_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.07"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/SHOULDER_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Shoulder_X_R"
    type="revolute">
    <origin
      xyz="0 -0.075202 0"
      rpy="0.349066 0 0" />
    <parent
      link="SCAPULA_R" />
    <child
      link="SHOULDER_R" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-3.1067"
      upper="0.034907"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="UPPERARM_R">
    <inertial>
      <origin
        xyz="-0.000572105970359116 -0.000197782630278198 -0.073521076099297"
        rpy="0 0 0" />
      <mass
        value="1.68411959490405" />
      <inertia
        ixx="0.00185791746257529"
        ixy="-7.81697721442655E-09"
        ixz="-4.9912030371148E-06"
        iyy="0.00192689292215647"
        iyz="3.91513423192929E-06"
        izz="0.000957744127550756" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/UPPERARM_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.045"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.045" length="0.12"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/UPPERARM_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Shoulder_Z_R"
    type="revolute">
    <origin
      xyz="0 0 -0.0845"
      rpy="0 0 0" />
    <parent
      link="SHOULDER_R" />
    <child
      link="UPPERARM_R" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.1067"
      upper="3.1067"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="FOREARM_R">
    <inertial>
      <origin
        xyz="-0.00302269801209659 0.00211336617492769 -0.0303480761371251"
        rpy="0 0 0" />
      <mass
        value="0.210669806739935" />
      <inertia
        ixx="0.00024447164836031"
        ixy="1.14129837582377E-06"
        ixz="-1.19593699916676E-06"
        iyy="0.000196741700106646"
        iyz="5.47089766964889E-05"
        izz="0.000233900128357954" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/FOREARM_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.06"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/FOREARM_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Elbow_R"
    type="revolute">
    <origin
      xyz="0 0 -0.1555"
      rpy="0 0 0" />
    <parent
      link="UPPERARM_R" />
    <child
      link="FOREARM_R" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.0944"
      upper="0.034907"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_REVOLUTE_R">
    <inertial>
      <origin
        xyz="-2.52805287587199E-06 -0.00103307088812676 -0.0647874989908417"
        rpy="0 0 0" />
      <mass
        value="1.37740752035531" />
      <inertia
        ixx="0.00107360569104927"
        ixy="-7.53890856919719E-08"
        ixz="1.36916323015848E-06"
        iyy="0.00109351435105507"
        iyz="-6.3555294947197E-06"
        izz="0.000919943910008151" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_REVOLUTE_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 -0.035"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.045" length="0.11"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_REVOLUTE_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_Z_R"
    type="revolute">
    <origin
      xyz="0 0 -0.0685"
      rpy="0 0 0" />
    <parent
      link="FOREARM_R" />
    <child
      link="WRIST_REVOLUTE_R" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-3.1067"
      upper="3.1067"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_UPDOWN_R">
    <inertial>
      <origin
        xyz="0.00177576479335967 -0.000946193504494486 -0.0498664228301069"
        rpy="0 0 0" />
      <mass
        value="0.375225598805297" />
      <inertia
        ixx="0.000182863630395187"
        ixy="-3.46520601712896E-06"
        ixz="8.20641817871579E-06"
        iyy="0.000181603976527201"
        iyz="3.14162822085518E-05"
        izz="0.000143288029999864" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_UPDOWN_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.05"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_UPDOWN_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_Y_R"
    type="revolute">
    <origin
      xyz="0 0 -0.1385"
      rpy="0 0 0" />
    <parent
      link="WRIST_REVOLUTE_R" />
    <child
      link="WRIST_UPDOWN_R" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-1.5708"
      upper="1.5708"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WRIST_FLANGE_R">
    <inertial>
      <origin
        xyz="3.29186008936055E-05 -0.000269851540685762 -0.0220552194886257"
        rpy="0 0 0" />
      <mass
        value="0.0624482400878027" />
      <inertia
        ixx="3.13229329970473E-05"
        ixy="-1.61269601396121E-07"
        ixz="-9.99014306342435E-06"
        iyy="4.02402164909269E-05"
        iyz="-3.50802274557633E-07"
        izz="3.08668335227516E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_FLANGE_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>  <!--存疑-->
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.04"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WRIST_FLANGE_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Wrist_X_R"
    type="revolute">
    <origin
      xyz="0 0 -0.063"
      rpy="0 0 0" />
    <parent
      link="WRIST_UPDOWN_R" />
    <child
      link="WRIST_FLANGE_R" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="-1.5708"
      upper="1.5708"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="TCP_R">
    <inertial>
      <origin
        xyz="0.000707102741450416 -0.000327581740715954 5.75097657451451E-06"
        rpy="0 0 0" />
      <mass
        value="0.0211623513252476" />
      <inertia
        ixx="2.79714844875998E-06"
        ixy="-2.42311423797055E-09"
        ixz="5.84282489742163E-10"
        iyy="2.82471924416795E-06"
        iyz="-1.22371537554282E-10"
        izz="5.43098748888509E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/TCP_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <collision>  <!--存疑-->
      <origin
        xyz="0 0 -0.01"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.025" length="0.06"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/TCP_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Tcp_R"
    type="fixed">
    <origin
      xyz="0 0 -0.060997"
      rpy="0 0 0" />
    <parent
      link="WRIST_FLANGE_R" />
    <child
      link="TCP_R" />
    <axis
      xyz="0 0 0" />
  </joint>



  <!-- <link name="CatchPoint_R" />
  <joint name="catchpoint_R" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.025 -0.10"/>
    <parent link="TCP_R"/>
    <child link="CatchPoint_R"/>
  </joint> -->



  <link
    name="HAND_R">
    <inertial>
      <origin
        xyz="-0.00760380870648292 -0.0030552965858972 0.0699562526473776"
        rpy="0 0 0" />
      <mass
        value="0.599204482000802" />
      <inertia
        ixx="0.000272450588803696"
        ixy="-4.72405878659601E-05"
        ixz="3.79133813253696E-05"
        iyy="0.000444470371522055"
        iyz="1.93663944432029E-05"
        izz="0.000248497826022798" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HAND_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.792156862745098 0.819607843137255 0.933333333333333 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0.02 -0.01 0.12"
        rpy="0 0 0" />
      <geometry>
        <sphere radius="0.08" />
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HAND_R.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Hand_R"
    type="fixed">
    <origin
      xyz="0 0 0"
      rpy="3.1416 0 -3.1416" />
    <parent
      link="TCP_R" />
    <child
      link="HAND_R" />
    <axis
      xyz="0 0 0" />
  </joint>

  <link
    name="NECK">
    <inertial>
      <origin
        xyz="-0.00012115688057085 0.000115504936864519 0.0384194913123284"
        rpy="0 0 0" />
      <mass
        value="0.257410493836562" />
      <inertia
        ixx="4.35116739632781E-05"
        ixy="-5.40138447466343E-10"
        ixz="1.08317942886687E-08"
        iyy="6.56746070988324E-05"
        iyz="3.47862212255702E-06"
        izz="3.92116071547786E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/NECK.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/NECK.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="Neck_Z"
    type="revolute">
    <origin
      xyz="0 0 0.531"
      rpy="0 0 0" />
    <parent
      link="CHEST" />
    <child
      link="NECK" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-0.7854"
      upper="0.7854"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="HEAD">
    <inertial>
      <origin
        xyz="0.0160311575334667 -0.000718601466054528 0.0818051192488338"
        rpy="0 0 0" />
      <mass
        value="0.815119442824952" />
      <inertia
        ixx="0.0014009268013237"
        ixy="-2.13741622838879E-06"
        ixz="-6.70966873921109E-05"
        iyy="0.00112638581878731"
        iyz="-8.83890127847117E-06"
        izz="0.0012804710515771" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HEAD.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.890196078431372 0.890196078431372 0.913725490196078 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0.02 0 0.06"
        rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.09" length="0.2"/>
        <!-- <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/HEAD.STL" /> -->
      </geometry>
    </collision>
  </link>
  <joint
    name="Neck_Y"
    type="revolute">
    <origin
      xyz="0 0 0.042"
      rpy="0 0 0" />
    <parent
      link="NECK" />
    <child
      link="HEAD" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-0.17453"
      upper="0.34907"
      effort="1000.0"
      velocity="0.5" />
  </joint>
  <link
    name="WHEEL_R">
    <inertial>
      <origin
        xyz="-2.19392721192459E-07 -0.0203004440220667 -8.05129418690598E-09"
        rpy="0 0 0" />
      <mass
        value="1.39725560013618" />
      <inertia
        ixx="0.00301111397703709"
        ixy="6.36100774497801E-09"
        ixz="-1.29217052739878E-06"
        iyy="0.00526425858211608"
        iyz="3.27914656267313E-10"
        izz="0.00301300566442609" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WHEEL_R.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WHEEL_R.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="Wheel_Y_R"
    type="fixed">
    <origin
      xyz="0 -0.21 -0.16"
      rpy="0 0 0" />
    <parent
      link="BASE" />
    <child
      link="WHEEL_R" />
    <axis
      xyz="0 1 0" />
  </joint>
  <link
    name="WHEEL_L">
    <inertial>
      <origin
        xyz="-2.19392721988697E-07 0.0203004440220743 8.05129587999609E-09"
        rpy="0 0 0" />
      <mass
        value="1.39725560013617" />
      <inertia
        ixx="0.00301111397703709"
        ixy="-6.36100783556753E-09"
        ixz="1.29217052739872E-06"
        iyy="0.00526425858211608"
        iyz="3.27914710071335E-10"
        izz="0.00301300566442609" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WHEEL_L.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <!-- <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://ocs2_robotic_assets/resources/wa1_mm/meshes/WHEEL_L.STL" />
      </geometry>
    </collision> -->
  </link>
  <joint
    name="Wheel_Y_L"
    type="fixed">
    <origin
      xyz="0 0.21 -0.16"
      rpy="0 0 0" />
    <parent
      link="BASE" />
    <child
      link="WHEEL_L" />
    <axis
      xyz="0 1 0" />
  </joint>
  
</robot>