import numpy as np
import pickle
import os

class normalizer():
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.x_mean = np.mean(x, axis=0)
        self.x_std = np.std(x, axis=0)
        self.y_mean = np.mean(y, axis=0)
        self.y_std = np.std(y, axis=0)

    def normalize(self, data, is_train_x=True):
        if is_train_x:
            return (data - self.x_mean) / self.x_std
        else:
            return (data - self.y_mean) / self.y_std

    def denormalize(self, data, is_train_x=True):
        if is_train_x:
            return data * self.x_std + self.x_mean
        else:
            return data * self.y_std + self.y_mean

    def save(self, filepath):
        """保存归一化器到文件"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)

    @classmethod
    def load(cls, filepath):
        """从文件加载归一化器"""
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    
    