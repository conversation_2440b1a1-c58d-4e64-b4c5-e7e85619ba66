import numpy as np

class normalizer():
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.x_mean = np.mean(x, axis=0)
        self.x_std = np.std(x, axis=0)
        self.y_mean = np.mean(y, axis=0)
        self.y_std = np.std(y, axis=0)

    def normalize(self, x, is_train_x=True):
        if is_train_x:
            return (x - self.x_mean) / self.x_std
        else:
            return (x - self.y_mean) / self.y_std
    
    def denormalize(self, x, is_train_x=True):
        if is_train_x:
            return x * self.x_std + self.x_mean
        else:
            return x * self.y_std + self.y_mean