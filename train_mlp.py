import numpy as np
import torch
from torch.utils.data import TensorDataset, DataLoader
import torch.nn as nn
from utils import normalizer

# 1. 读取数据
pose6d = np.load('filtered_pose6d_array.npy')  # (N, 6)
q_array = np.load('filtered_q_array_origin.npy')  # (N, 20)
right_arm_indices = list(range(13, 20))  # 13-19, 共7个关节
right_arm_q = q_array[:, right_arm_indices]  # (N, 7)

# 2. 转为Tensor
# X = torch.from_numpy(pose6d.astype('float32'))
# y = torch.from_numpy(right_arm_q.astype('float32'))

norm = normalizer(pose6d, right_arm_q)
X_normalized = norm.normalize(pose6d, is_train_x=True)
y_normalized = norm.normalize(right_arm_q, is_train_x=False)

# 3. 转为Tensor
X = torch.from_numpy(X_normalized.astype('float32'))
y = torch.from_numpy(y_normalized.astype('float32'))

# 3. 划分训练/验证集
N = X.shape[0]
split = int(N * 0.8)
X_train, X_val = X[:split], X[split:]
y_train, y_val = y[:split], y[split:]
train_ds = TensorDataset(X_train, y_train)
val_ds = TensorDataset(X_val, y_val)
train_loader = DataLoader(train_ds, batch_size=64, shuffle=True)
val_loader = DataLoader(val_ds, batch_size=64)

# 4. 定义MLP
class MLP(nn.Module):
    def __init__(self, in_dim=6, out_dim=7, hidden_dim=128):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(in_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, out_dim)
        )
    def forward(self, x):
        return self.net(x)

# 5. 训练
if __name__ == "__main__":
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    model = MLP().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    loss_fn = nn.MSELoss()

    n_epochs = 10000
    for epoch in range(n_epochs):
        model.train()
        for xb, yb in train_loader:
            xb, yb = xb.to(device), yb.to(device)
            pred = model(xb)
            loss = loss_fn(pred, yb)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        # 验证
        model.eval()
        with torch.no_grad():
            val_loss = 0
            for xb, yb in val_loader:
                xb, yb = xb.to(device), yb.to(device)
                pred = model(xb)
                val_loss += loss_fn(pred, yb).item() * xb.size(0)
            val_loss /= len(val_ds)
        if (epoch+1) % 100 == 0 or epoch == 0:
            print(f"Epoch {epoch+1}/{n_epochs}, Train Loss: {loss.item():.6f}, Val Loss: {val_loss:.6f}")

    # 6. 推理示例
    model.eval()
    n_show = 5  # 多预测几组

    with torch.no_grad():
        for i in range(n_show):
            test_pose = X_val[i:i+1].to(device)
            pred_q_norm = model(test_pose)
            # 反归一化
            pred_q = norm.denormalize(pred_q_norm.cpu().numpy(), is_train_x=False)
            true_q = norm.denormalize(y_val[i:i+1].cpu().numpy(), is_train_x=False)
            print(f"样本 {i}:")
            print("  预测的右臂7关节角度：", np.round(pred_q, 3))
            print("  真实的右臂7关节角度：", np.round(true_q, 3))